{% extends "base/base.html" %}
{% load static %}

{% block title %}{{ page_title|default:"Comptabilité" }} - Lotus Web{% endblock %}

{% block extra_css %}
<link href="{% static 'accounting/css/accounting.css' %}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Breadcrumb -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">{{ page_title|default:"Comptabilité" }}</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">Accueil</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'accounting:dashboard' %}">Comptabilité</a></li>
                        {% if page_title != "Dashboard Comptable" %}
                        <li class="breadcrumb-item active">{{ page_title }}</li>
                        {% endif %}
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages -->
    {% if messages %}
    <div class="row">
        <div class="col-12">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags|default:'info' }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Contenu principal -->
    {% block accounting_content %}
    {% endblock %}
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'accounting/js/accounting.js' %}"></script>
{% block accounting_js %}
{% endblock %}
{% endblock %}
