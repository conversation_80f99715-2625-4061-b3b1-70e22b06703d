{% extends 'accounting/base.html' %}
{% load static %}
{% load humanize %}

{% block accounting_content %}
<!-- En-tête avec infos société -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="flex-grow-1">
                        <h4 class="card-title mb-1">{{ societe.nom|default:"Société non configurée" }}</h4>
                        <p class="text-muted mb-0">
                            {% if societe.devise %}
                            Devise: {{ societe.devise }} | 
                            {% endif %}
                            {% if societe.regimeFiscal %}
                            Régime: {{ societe.regimeFiscal }}
                            {% else %}
                            Régime: Non défini
                            {% endif %}
                        </p>
                    </div>
                    <div class="flex-shrink-0">
                        <button type="button" class="btn btn-outline-primary btn-sm" id="refreshKpis">
                            <i class="bi bi-arrow-clockwise me-1"></i>Actualiser
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- KPIs Financiers -->
<div class="row mb-4" id="kpisContainer">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">Chiffre d'Affaires</h5>
                <h3 class="text-primary" id="kpiCA">
                    {% if kpis.chiffreAffaires %}
                        {{ kpis.chiffreAffaires|floatformat:0|intcomma }} FCFA
                    {% else %}
                        0 FCFA
                    {% endif %}
                </h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">Résultat Net</h5>
                <h3 class="text-success" id="kpiResultat">
                    {% if kpis.resultatNet %}
                        {{ kpis.resultatNet|floatformat:0|intcomma }} FCFA
                    {% else %}
                        0 FCFA
                    {% endif %}
                </h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">Marge Commerciale</h5>
                <h3 class="text-info" id="kpiMarge">
                    {% if kpis.margeCommerciale %}
                        {{ kpis.margeCommerciale|floatformat:1 }}%
                    {% else %}
                        0%
                    {% endif %}
                </h3>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">Ratio Liquidité</h5>
                <h3 class="text-warning" id="kpiLiquidite">
                    {% if kpis.ratioLiquidite %}
                        {{ kpis.ratioLiquidite|floatformat:2 }}
                    {% else %}
                        0
                    {% endif %}
                </h3>
            </div>
        </div>
    </div>
</div>

<!-- Alertes -->
{% if alertes %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Alertes Financières
                </h5>
            </div>
            <div class="card-body">
                {% for alerte in alertes %}
                <div class="alert alert-{% if alerte.niveau == 'CRITICAL' %}danger{% elif alerte.niveau == 'WARNING' %}warning{% else %}info{% endif %}" role="alert">
                    <strong>{{ alerte.type }}:</strong> {{ alerte.message }}
                    {% if alerte.valeur %}
                    <br><small>Valeur: {{ alerte.valeur|floatformat:0|intcomma }} FCFA</small>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Actions rapides -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Actions Rapides</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'accounting:ecriture_create' %}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>Nouvelle Écriture
                    </a>
                    <a href="{% url 'accounting:balance' %}" class="btn btn-outline-primary">
                        <i class="bi bi-table me-2"></i>Balance Générale
                    </a>
                    <a href="{% url 'accounting:lettrage' %}" class="btn btn-outline-secondary">
                        <i class="bi bi-link-45deg me-2"></i>Lettrage
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">États Comptables</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{% url 'accounting:bilan' %}" class="btn btn-outline-success">
                        <i class="bi bi-graph-up me-2"></i>Bilan Comptable
                    </a>
                    <a href="{% url 'accounting:compte_resultat' %}" class="btn btn-outline-info">
                        <i class="bi bi-bar-chart me-2"></i>Compte de Résultat
                    </a>
                    <a href="{% url 'accounting:grand_livre' %}" class="btn btn-outline-warning">
                        <i class="bi bi-book me-2"></i>Grand Livre
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test de connexion API (en développement) -->
{% if debug %}
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Test API (Développement)</h5>
            </div>
            <div class="card-body">
                <button type="button" class="btn btn-info btn-sm" id="testApiConnection">
                    <i class="bi bi-wifi me-1"></i>Tester Connexion API
                </button>
                <div id="apiTestResult" class="mt-2"></div>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block accounting_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Actualisation des KPIs
    document.getElementById('refreshKpis')?.addEventListener('click', function() {
        refreshKpis();
    });
    
    // Test de connexion API (développement)
    document.getElementById('testApiConnection')?.addEventListener('click', function() {
        testApiConnection();
    });
    
    // Actualisation automatique toutes les 5 minutes
    setInterval(refreshKpis, 300000);
});

function refreshKpis() {
    fetch('{% url "accounting:get_kpis_api" %}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateKpisDisplay(data.data);
            } else {
                console.error('Erreur KPIs:', data.error);
            }
        })
        .catch(error => {
            console.error('Erreur réseau:', error);
        });
}

function updateKpisDisplay(kpis) {
    if (kpis.chiffreAffaires !== undefined) {
        document.getElementById('kpiCA').textContent = 
            new Intl.NumberFormat('fr-FR').format(kpis.chiffreAffaires) + ' FCFA';
    }
    if (kpis.resultatNet !== undefined) {
        document.getElementById('kpiResultat').textContent = 
            new Intl.NumberFormat('fr-FR').format(kpis.resultatNet) + ' FCFA';
    }
    if (kpis.margeCommerciale !== undefined) {
        document.getElementById('kpiMarge').textContent = 
            kpis.margeCommerciale.toFixed(1) + '%';
    }
    if (kpis.ratioLiquidite !== undefined) {
        document.getElementById('kpiLiquidite').textContent = 
            kpis.ratioLiquidite.toFixed(2);
    }
}

function testApiConnection() {
    const resultDiv = document.getElementById('apiTestResult');
    resultDiv.innerHTML = '<div class="spinner-border spinner-border-sm me-2"></div>Test en cours...';
    
    fetch('{% url "accounting:test_api_connection" %}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                resultDiv.innerHTML = `
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle me-2"></i>${data.message}
                        <br><small>Société: ${data.data.societe.nom || 'Non définie'}</small>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="bi bi-x-circle me-2"></i>${data.error}
                    </div>
                `;
            }
        })
        .catch(error => {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-x-circle me-2"></i>Erreur réseau: ${error.message}
                </div>
            `;
        });
}
</script>
{% endblock %}
