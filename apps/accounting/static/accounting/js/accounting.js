// apps/accounting/static/accounting/js/accounting.js

/**
 * Utilitaires JavaScript pour le module comptabilité
 */

// Configuration globale
const AccountingJS = {
    // URLs API (seront injectées par Django)
    apiUrls: {},
    
    // Configuration
    config: {
        currency: 'FCFA',
        decimalPlaces: 0,
        thousandSeparator: ' '
    },
    
    // État global
    state: {
        loading: false,
        currentForm: null
    }
};

/**
 * Utilitaires de formatage
 */
AccountingJS.formatCurrency = function(amount, currency = 'FCFA') {
    if (amount === null || amount === undefined) return '0 ' + currency;
    
    const formatted = new Intl.NumberFormat('fr-FR', {
        minimumFractionDigits: 0,
        maximumFractionDigits: 2
    }).format(amount);
    
    return formatted + ' ' + currency;
};

AccountingJS.formatDate = function(dateString) {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR');
};

AccountingJS.formatNumber = function(number, decimals = 2) {
    if (number === null || number === undefined) return '0';
    
    return new Intl.NumberFormat('fr-FR', {
        minimumFractionDigits: 0,
        maximumFractionDigits: decimals
    }).format(number);
};

/**
 * Gestion des états de chargement
 */
AccountingJS.showLoading = function(element) {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    if (element) {
        element.classList.add('loading');
    }
};

AccountingJS.hideLoading = function(element) {
    if (typeof element === 'string') {
        element = document.querySelector(element);
    }
    if (element) {
        element.classList.remove('loading');
    }
};

/**
 * Gestion des messages
 */
AccountingJS.showMessage = function(message, type = 'info', container = '.messages-container') {
    const containerEl = document.querySelector(container);
    if (!containerEl) return;
    
    const alertEl = document.createElement('div');
    alertEl.className = `alert alert-${type} alert-dismissible fade show`;
    alertEl.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    containerEl.appendChild(alertEl);
    
    // Auto-dismiss après 5 secondes
    setTimeout(() => {
        if (alertEl.parentNode) {
            alertEl.remove();
        }
    }, 5000);
};

/**
 * Validation des comptes SYSCOHADA
 */
AccountingJS.validateSyscohadaAccount = function(numero) {
    if (!numero || typeof numero !== 'string') return false;
    
    // Doit être numérique
    if (!/^\d+$/.test(numero)) return false;
    
    // Longueur entre 3 et 10 chiffres
    if (numero.length < 3 || numero.length > 10) return false;
    
    // Classe valide (1-8)
    const classe = parseInt(numero[0]);
    return classe >= 1 && classe <= 8;
};

/**
 * Validation d'équilibrage d'écriture
 */
AccountingJS.validateEquilibrage = function(lignes) {
    let totalDebit = 0;
    let totalCredit = 0;
    
    lignes.forEach(ligne => {
        totalDebit += parseFloat(ligne.debit || 0);
        totalCredit += parseFloat(ligne.credit || 0);
    });
    
    const difference = Math.abs(totalDebit - totalCredit);
    
    return {
        equilibre: difference < 0.01,
        totalDebit: totalDebit,
        totalCredit: totalCredit,
        difference: difference
    };
};

/**
 * Auto-complétion des comptes
 */
AccountingJS.initCompteAutocomplete = function(inputSelector, options = {}) {
    const inputs = document.querySelectorAll(inputSelector);
    
    inputs.forEach(input => {
        let timeout;
        
        input.addEventListener('input', function() {
            clearTimeout(timeout);
            const query = this.value;
            
            if (query.length < 2) return;
            
            timeout = setTimeout(() => {
                AccountingJS.searchComptes(query, (results) => {
                    AccountingJS.showAutocompleteResults(input, results);
                });
            }, 300);
        });
        
        // Fermer l'autocomplétion en cliquant ailleurs
        document.addEventListener('click', function(e) {
            if (!input.contains(e.target)) {
                AccountingJS.hideAutocompleteResults(input);
            }
        });
    });
};

AccountingJS.searchComptes = function(query, callback) {
    // TODO: Implémenter la recherche via API
    // Pour l'instant, retourner un tableau vide
    callback([]);
};

AccountingJS.showAutocompleteResults = function(input, results) {
    // Supprimer les résultats existants
    AccountingJS.hideAutocompleteResults(input);
    
    if (results.length === 0) return;
    
    const resultsContainer = document.createElement('div');
    resultsContainer.className = 'autocomplete-results';
    resultsContainer.style.cssText = `
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ddd;
        border-top: none;
        max-height: 200px;
        overflow-y: auto;
        z-index: 1000;
    `;
    
    results.forEach(compte => {
        const item = document.createElement('div');
        item.className = 'autocomplete-item';
        item.style.cssText = 'padding: 8px 12px; cursor: pointer; border-bottom: 1px solid #eee;';
        item.innerHTML = `<strong>${compte.numero}</strong> - ${compte.libelle}`;
        
        item.addEventListener('click', () => {
            input.value = compte.numero;
            AccountingJS.hideAutocompleteResults(input);
            
            // Déclencher l'événement change
            input.dispatchEvent(new Event('change'));
        });
        
        item.addEventListener('mouseenter', () => {
            item.style.backgroundColor = '#f8f9fa';
        });
        
        item.addEventListener('mouseleave', () => {
            item.style.backgroundColor = 'white';
        });
        
        resultsContainer.appendChild(item);
    });
    
    // Positionner le conteneur
    const inputRect = input.getBoundingClientRect();
    input.parentNode.style.position = 'relative';
    input.parentNode.appendChild(resultsContainer);
};

AccountingJS.hideAutocompleteResults = function(input) {
    const existing = input.parentNode.querySelector('.autocomplete-results');
    if (existing) {
        existing.remove();
    }
};

/**
 * Gestion des formulaires d'écriture
 */
AccountingJS.initEcritureForm = function() {
    const form = document.querySelector('.ecriture-form');
    if (!form) return;
    
    // Initialiser l'auto-complétion des comptes
    AccountingJS.initCompteAutocomplete('.compte-input');
    
    // Validation en temps réel
    form.addEventListener('input', AccountingJS.validateEcritureForm);
    
    // Boutons d'ajout/suppression de lignes
    AccountingJS.initLigneButtons();
};

AccountingJS.validateEcritureForm = function() {
    const lignes = AccountingJS.getLignesFromForm();
    const validation = AccountingJS.validateEquilibrage(lignes);
    
    AccountingJS.updateTotauxDisplay(validation);
    AccountingJS.updateFormValidation(validation.equilibre);
};

AccountingJS.getLignesFromForm = function() {
    const lignes = [];
    const ligneElements = document.querySelectorAll('.ligne-ecriture');
    
    ligneElements.forEach(element => {
        const debit = parseFloat(element.querySelector('.debit-input')?.value || 0);
        const credit = parseFloat(element.querySelector('.credit-input')?.value || 0);
        
        if (debit > 0 || credit > 0) {
            lignes.push({ debit, credit });
        }
    });
    
    return lignes;
};

AccountingJS.updateTotauxDisplay = function(validation) {
    const totauxContainer = document.querySelector('.totaux-ecriture');
    if (!totauxContainer) return;
    
    const totalDebitEl = totauxContainer.querySelector('.total-debit');
    const totalCreditEl = totauxContainer.querySelector('.total-credit');
    const statusEl = totauxContainer.querySelector('.equilibrage-status');
    
    if (totalDebitEl) {
        totalDebitEl.textContent = AccountingJS.formatCurrency(validation.totalDebit);
    }
    
    if (totalCreditEl) {
        totalCreditEl.textContent = AccountingJS.formatCurrency(validation.totalCredit);
    }
    
    if (statusEl) {
        statusEl.textContent = validation.equilibre ? 'Équilibrée' : 'Déséquilibrée';
        statusEl.className = validation.equilibre ? 'total-equilibre' : 'total-desequilibre';
    }
};

AccountingJS.updateFormValidation = function(isValid) {
    const submitBtn = document.querySelector('.submit-ecriture');
    if (submitBtn) {
        submitBtn.disabled = !isValid;
    }
    
    const form = document.querySelector('.ecriture-form');
    if (form) {
        form.classList.toggle('equilibre', isValid);
        form.classList.toggle('desequilibre', !isValid);
    }
};

AccountingJS.initLigneButtons = function() {
    // Bouton d'ajout de ligne
    document.addEventListener('click', function(e) {
        if (e.target.matches('.add-ligne-btn')) {
            AccountingJS.addLigneEcriture();
        }
        
        if (e.target.matches('.remove-ligne-btn')) {
            AccountingJS.removeLigneEcriture(e.target);
        }
    });
};

AccountingJS.addLigneEcriture = function() {
    // TODO: Implémenter l'ajout de ligne dynamique
    console.log('Ajout de ligne d\'écriture');
};

AccountingJS.removeLigneEcriture = function(button) {
    const ligne = button.closest('.ligne-ecriture');
    if (ligne) {
        ligne.remove();
        AccountingJS.validateEcritureForm();
    }
};

/**
 * Initialisation au chargement de la page
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialiser les formulaires d'écriture
    AccountingJS.initEcritureForm();
    
    // Initialiser les tooltips Bootstrap
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});

// Exposer AccountingJS globalement
window.AccountingJS = AccountingJS;
