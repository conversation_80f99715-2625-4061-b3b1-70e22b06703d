/* apps/accounting/static/accounting/css/accounting.css */

/* Variables CSS pour la comptabilité */
:root {
    --accounting-primary: #0d6efd;
    --accounting-success: #198754;
    --accounting-danger: #dc3545;
    --accounting-warning: #ffc107;
    --accounting-info: #0dcaf0;
    --accounting-light: #f8f9fa;
    --accounting-dark: #212529;
}

/* Styles généraux pour le module comptabilité */
.accounting-section {
    background-color: var(--accounting-light);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

/* KPIs Dashboard */
.kpi-card {
    transition: transform 0.2s ease-in-out;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.kpi-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.kpi-value {
    font-size: 2rem;
    font-weight: bold;
    margin: 0.5rem 0;
}

/* Plan comptable */
.compte-tree {
    list-style: none;
    padding-left: 0;
}

.compte-tree li {
    margin: 0.25rem 0;
}

.compte-tree .compte-item {
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.compte-tree .compte-item:hover {
    background-color: var(--accounting-light);
}

.compte-tree .compte-parent {
    font-weight: bold;
    background-color: #e9ecef;
}

.compte-tree .compte-child {
    margin-left: 1.5rem;
    border-left: 2px solid #dee2e6;
    padding-left: 1rem;
}

/* Formulaires d'écritures */
.ecriture-form {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.ligne-ecriture {
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #f8f9fa;
}

.ligne-ecriture.equilibre {
    border-color: var(--accounting-success);
    background-color: #d1e7dd;
}

.ligne-ecriture.desequilibre {
    border-color: var(--accounting-danger);
    background-color: #f8d7da;
}

/* Totaux d'écriture */
.totaux-ecriture {
    background-color: #e9ecef;
    border-radius: 4px;
    padding: 1rem;
    margin-top: 1rem;
}

.total-debit, .total-credit {
    font-size: 1.1rem;
    font-weight: bold;
}

.total-equilibre {
    color: var(--accounting-success);
}

.total-desequilibre {
    color: var(--accounting-danger);
}

/* États comptables */
.etat-comptable {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.etat-comptable table {
    margin-bottom: 0;
}

.etat-comptable .table-header {
    background-color: var(--accounting-primary);
    color: white;
}

.balance-row {
    border-bottom: 1px solid #dee2e6;
}

.balance-row:hover {
    background-color: var(--accounting-light);
}

.balance-total {
    background-color: #e9ecef;
    font-weight: bold;
    border-top: 2px solid var(--accounting-primary);
}

/* Lettrage */
.lettrage-container {
    background-color: white;
    border-radius: 8px;
    padding: 1.5rem;
}

.ligne-lettrable {
    cursor: pointer;
    transition: background-color 0.2s;
}

.ligne-lettrable:hover {
    background-color: var(--accounting-light);
}

.ligne-lettrable.selected {
    background-color: #cff4fc;
    border-color: var(--accounting-info);
}

.ligne-lettree {
    background-color: #d1e7dd;
    color: #0f5132;
}

/* Alertes financières */
.alerte-financiere {
    border-left: 4px solid;
    padding: 1rem;
    margin-bottom: 1rem;
    border-radius: 0 4px 4px 0;
}

.alerte-critical {
    border-left-color: var(--accounting-danger);
    background-color: #f8d7da;
}

.alerte-warning {
    border-left-color: var(--accounting-warning);
    background-color: #fff3cd;
}

.alerte-info {
    border-left-color: var(--accounting-info);
    background-color: #d1ecf1;
}

/* Responsive */
@media (max-width: 768px) {
    .kpi-value {
        font-size: 1.5rem;
    }
    
    .compte-tree .compte-child {
        margin-left: 1rem;
        padding-left: 0.5rem;
    }
    
    .ecriture-form {
        padding: 1rem;
    }
    
    .ligne-ecriture {
        padding: 0.75rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

/* Loading states */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: inherit;
}

.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--accounting-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    z-index: 1;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
